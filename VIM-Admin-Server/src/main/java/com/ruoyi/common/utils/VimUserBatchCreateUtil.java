package com.ruoyi.common.utils;

import com.ruoyi.project.VimUserSys.domain.VimUser;
import com.ruoyi.project.VimUserSys.mapper.VimUserMapper;
import com.ruoyi.project.VimUserSys.utils.HashUtil;
import com.ruoyi.project.VimServerSeedSys.service.IVimSeedService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;

/**
 * VIM系统用户批量创建工具类
 * 
 * 功能：批量创建vim_user表的用户记录
 * 特点：
 * 1. 二次元风格昵称生成
 * 2. 随机头像分配
 * 3. 唯一性约束保证
 * 4. 事务安全
 * 5. 详细执行日志
 * 
 * <AUTHOR> System
 * @date 2025-01-03
 */
@Component
public class VimUserBatchCreateUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(VimUserBatchCreateUtil.class);
    
    @Autowired
    private VimUserMapper vimUserMapper;
    
    @Autowired
    private IVimSeedService vimSeedService;
    
    // 二次元风格昵称前缀
    private static final String[] ANIME_PREFIXES = {
        "星空", "月影", "樱花", "雪音", "夜神", "天使", "魔法", "幻想",
        "梦境", "彩虹", "流星", "晨曦", "暮光", "银河", "水晶", "翡翠",
        "紫罗兰", "蔷薇", "百合", "向日葵", "薰衣草", "茉莉", "玫瑰", "桃花",
        "冰雪", "火焰", "雷电", "风暴", "海洋", "森林", "山峰", "云朵"
    };
    
    // 二次元风格昵称后缀
    private static final String[] ANIME_SUFFIXES = {
        "酱", "君", "桑", "殿", "姬", "子", "美", "香", "花", "音",
        "雪", "月", "星", "光", "影", "梦", "心", "灵", "魂", "翼",
        "羽", "瞳", "泪", "笑", "歌", "舞", "诗", "画", "琴", "剑",
        "法师", "骑士", "公主", "王子", "天使", "恶魔", "精灵", "龙"
    };
    
    // 头像URL列表
    private static final String[] AVATAR_URLS = {
        "http://www.voltskins.top/image/userimages/01.png",
        "http://www.voltskins.top/image/userimages/02.png",
        "http://www.voltskins.top/image/userimages/03.png",
        "http://www.voltskins.top/image/userimages/04.png",
        "http://www.voltskins.top/image/userimages/05.png",
        "http://www.voltskins.top/image/userimages/06.png",
        "http://www.voltskins.top/image/userimages/07.png",
        "http://www.voltskins.top/image/userimages/08.png"
    };
    
    private final Random random = new Random();
    
    /**
     * 批量创建VIM用户
     * 
     * @param count 创建用户数量
     * @return 创建结果信息
     */
    @Transactional(rollbackFor = Exception.class)
    public BatchCreateResult batchCreateUsers(int count) {
        logger.info("开始批量创建{}个VIM用户", count);
        
        if (count <= 0 || count > 1000) {
            throw new IllegalArgumentException("创建数量必须在1-1000之间");
        }
        
        BatchCreateResult result = new BatchCreateResult();
        result.setTotalCount(count);
        result.setStartTime(System.currentTimeMillis());
        
        List<String> successUsers = new ArrayList<>();
        List<String> failedUsers = new ArrayList<>();
        
        // 获取当前最大用户ID，确保从合适的起始点开始
        Long maxUserId = getMaxUserId();
        long startUserId = maxUserId != null ? maxUserId + 1 : 1;
        
        for (int i = 0; i < count; i++) {
            try {
                long currentUserId = startUserId + i;
                VimUser user = createSingleUser(currentUserId, i);
                
                int insertResult = vimUserMapper.insertVimUser(user);
                if (insertResult > 0) {
                    successUsers.add(user.getNickname());
                    logger.info("成功创建用户 [{}/{}]: {} (ID: {})", 
                        i + 1, count, user.getNickname(), user.getId());
                } else {
                    failedUsers.add("用户" + (i + 1) + " - 插入失败");
                    logger.error("插入用户失败: {}", user.getNickname());
                }
                
            } catch (Exception e) {
                String errorMsg = "用户" + (i + 1) + " - " + e.getMessage();
                failedUsers.add(errorMsg);
                logger.error("创建用户失败: {}", errorMsg, e);
            }
        }
        
        result.setSuccessCount(successUsers.size());
        result.setFailedCount(failedUsers.size());
        result.setSuccessUsers(successUsers);
        result.setFailedUsers(failedUsers);
        result.setEndTime(System.currentTimeMillis());
        result.setDuration(result.getEndTime() - result.getStartTime());
        
        logger.info("批量创建用户完成 - 成功: {}, 失败: {}, 耗时: {}ms", 
            result.getSuccessCount(), result.getFailedCount(), result.getDuration());
        
        return result;
    }
    
    /**
     * 创建单个用户
     */
    private VimUser createSingleUser(long userId, int index) {
        VimUser user = new VimUser();
        
        // 基础信息
        user.setId(userId);
        user.setPhone(generateUniquePhone(userId));
        user.setNickname(generateAnimeNickname(index));
        user.setUsername(generateUsername(userId));
        user.setPassword("123456"); // 默认密码，会被HashUtil加密
        
        // 随机头像
        user.setUserimage(getRandomAvatar());
        
        // 默认值设置
        user.setCoin(new BigDecimal("0.00"));
        user.setKey(new BigDecimal("0.00"));
        user.setAutokey(0); // 关闭自动锻造钥匙
        user.setIdentity(1); // 普通用户
        user.setIsauth(0); // 未实名认证
        user.setState(1); // 正常状态
        user.setExp(new BigDecimal("0.00"));
        user.setLevel(0);
        
        // 时间戳（秒级）
        long currentTime = Instant.now().getEpochSecond();
        user.setVimUsercreateTime(currentTime);
        
        // 生成邀请码
        user.setInviteCode(generateInviteCode(userId));
        
        return user;
    }
    
    /**
     * 生成二次元风格昵称
     */
    private String generateAnimeNickname(int index) {
        String prefix = ANIME_PREFIXES[random.nextInt(ANIME_PREFIXES.length)];
        String suffix = ANIME_SUFFIXES[random.nextInt(ANIME_SUFFIXES.length)];
        
        // 添加随机数字确保唯一性
        int randomNum = 1000 + random.nextInt(9000);
        return prefix + suffix + randomNum;
    }
    
    /**
     * 生成唯一手机号
     */
    private String generateUniquePhone(long userId) {
        // 使用1开头的11位手机号格式，确保唯一性
        return String.format("1%010d", userId);
    }
    
    /**
     * 生成用户名
     */
    private String generateUsername(long userId) {
        return "user" + userId;
    }
    
    /**
     * 随机选择头像
     */
    private String getRandomAvatar() {
        return AVATAR_URLS[random.nextInt(AVATAR_URLS.length)];
    }
    
    /**
     * 生成邀请码
     */
    private String generateInviteCode(long userId) {
        return "VIM" + String.format("%06d", userId);
    }
    
    /**
     * 获取当前最大用户ID
     */
    private Long getMaxUserId() {
        try {
            return vimUserMapper.selectMaxUserId();
        } catch (Exception e) {
            logger.warn("获取最大用户ID失败，将使用自动分配: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 批量创建结果类
     */
    public static class BatchCreateResult {
        private int totalCount;
        private int successCount;
        private int failedCount;
        private List<String> successUsers;
        private List<String> failedUsers;
        private long startTime;
        private long endTime;
        private long duration;
        
        // Getters and Setters
        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }
        
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        
        public int getFailedCount() { return failedCount; }
        public void setFailedCount(int failedCount) { this.failedCount = failedCount; }
        
        public List<String> getSuccessUsers() { return successUsers; }
        public void setSuccessUsers(List<String> successUsers) { this.successUsers = successUsers; }
        
        public List<String> getFailedUsers() { return failedUsers; }
        public void setFailedUsers(List<String> failedUsers) { this.failedUsers = failedUsers; }
        
        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }
        
        public long getEndTime() { return endTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
        
        public long getDuration() { return duration; }
        public void setDuration(long duration) { this.duration = duration; }
        
        @Override
        public String toString() {
            return String.format(
                "批量创建结果: 总数=%d, 成功=%d, 失败=%d, 耗时=%dms",
                totalCount, successCount, failedCount, duration
            );
        }
    }
}
