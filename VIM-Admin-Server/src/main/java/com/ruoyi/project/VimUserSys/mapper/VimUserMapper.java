package com.ruoyi.project.VimUserSys.mapper;

import java.util.List;
import com.ruoyi.project.VimUserSys.domain.VimUser;
import com.ruoyi.project.VimUserSys.domain.vo.SubordinateUserVO;

/**
 * 盲盒用户Mapper接口
 * 
 * <AUTHOR> and 催一催
 * @date 2025-04-17
 */
public interface VimUserMapper 
{
    /**
     * 查询盲盒用户
     * 
     * @param id 盲盒用户主键
     * @return 盲盒用户
     */
    public VimUser selectVimUserById(Long id);

    /**
     * 查询盲盒用户列表
     * 
     * @param vimUser 盲盒用户
     * @return 盲盒用户集合
     */
    List<VimUser> selectVimUserList(VimUser vimUser);
    /**
     * 新增盲盒用户
     * 
     * @param vimUser 盲盒用户
     * @return 结果
     */
    public int insertVimUser(VimUser vimUser);

    /**
     * 修改盲盒用户
     * 
     * @param vimUser 盲盒用户
     * @return 结果
     */
    public int updateVimUser(VimUser vimUser);

    /**
     * 删除盲盒用户
     * 
     * @param id 盲盒用户主键
     * @return 结果
     */
    public int deleteVimUserById(Long id);

    /**
     * 批量删除盲盒用户
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteVimUserByIds(Long[] ids);

    /**
     * 查询指定用户的下级用户列表
     *
     * @param inviteUserId 邀请人用户ID
     * @return 下级用户列表
     */
    public List<SubordinateUserVO> selectSubordinateUsers(Long inviteUserId);

    /**
     * 查询指定用户的下级用户列表（手动分页）
     *
     * @param inviteUserId 邀请人用户ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 下级用户列表
     */
    public List<SubordinateUserVO> selectSubordinateUsersWithPagination(@org.apache.ibatis.annotations.Param("inviteUserId") Long inviteUserId,
                                                                       @org.apache.ibatis.annotations.Param("offset") int offset,
                                                                       @org.apache.ibatis.annotations.Param("limit") int limit);

    /**
     * 统计指定用户的下级用户数量
     *
     * @param inviteUserId 邀请人用户ID
     * @return 下级用户数量
     */
    public int countSubordinateUsers(Long inviteUserId);

    /**
     * 检查用户是否已进行实名认证
     *
     * @param userId 用户ID
     * @return 实名认证记录数量（0表示未实名，1表示已实名）
     */
    public int checkUserAuthStatus(Long userId);

    /**
     * 查询最大用户ID
     *
     * @return 最大用户ID
     */
    public Long selectMaxUserId();
}
