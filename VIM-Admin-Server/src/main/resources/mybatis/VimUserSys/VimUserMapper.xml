<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.VimUserSys.mapper.VimUserMapper">
    
    <resultMap type="VimUser" id="VimUserResult">
        <result property="id"    column="id"    />
        <result property="phone"    column="phone"    />
        <result property="password"    column="password"    />
        <result property="nickname"    column="nickname"    />
        <result property="username"    column="username"    />
        <result property="userimage"    column="userimage"    />
        <result property="lastLoginTime"    column="last_login_time"    />
        <result property="lastLoginIp"    column="last_login_ip"    />
        <result property="coin"    column="coin"    />
        <result property="key"    column="key"    />
        <result property="inviteCode"    column="invite_code"    />
        <result property="inviteUser"    column="invite_user"    />
        <result property="vimUsercreateTime"    column="create_time"    />
        <result property="steamId"    column="steam_id"    />
        <result property="steamLink"    column="steam_link"    />
        <result property="exp"    column="exp"    />
        <result property="level"    column="level"    />
        <result property="seed"    column="seed"    />
        <result property="identity"    column="identity"    />
        <result property="isauth"    column="isauth"    />
        <result property="state"    column="state"    />
        <!-- 🆕 新增首充时间字段 -->
        <result property="firstRechargeTime"    column="first_recharge_time"    />
        <result property="firstRechargeTimeStr"    column="first_recharge_time_str"    />
    </resultMap>

    <sql id="selectVimUserVo">
        select id, phone, identity, password, nickname, username, userimage, last_login_time, last_login_ip, coin, `key`, invite_code, invite_user, create_time, steam_id, steam_link, exp, level, seed, isauth, state from vim_user
    </sql>

    <select id="selectVimUserList" parameterType="VimUser" resultMap="VimUserResult">
        SELECT
            vu.id, vu.phone, vu.identity, vu.password, vu.nickname, vu.username, vu.userimage,
            vu.last_login_time, vu.last_login_ip, vu.coin, vu.`key`, vu.invite_code, vu.invite_user,
            vu.create_time, vu.steam_id, vu.steam_link, vu.exp, vu.level, vu.seed, vu.isauth, vu.state,
            <!-- 🔑 关键：添加首充时间查询 -->
            first_recharge.create_time as first_recharge_time,
            CASE
                WHEN first_recharge.create_time IS NOT NULL
                THEN FROM_UNIXTIME(first_recharge.create_time, '%Y-%m-%d %H:%i:%s')
                ELSE NULL
            END as first_recharge_time_str
        FROM vim_user vu
        <!-- 🔑 LEFT JOIN首次充值记录：获取每个用户的第一条充值记录 -->
        LEFT JOIN (
            SELECT
                vor.uid,
                vor.create_time,
                ROW_NUMBER() OVER (PARTITION BY vor.uid ORDER BY vor.create_time ASC) as rn
            FROM vim_order_recharge vor
            WHERE vor.state = 2
        ) first_recharge ON vu.id = first_recharge.uid AND first_recharge.rn = 1
        <where>
            <if test="id != null "> and vu.id = #{id}</if>
            <if test="phone != null  and phone != ''"> and vu.phone like concat('%', #{phone}, '%')</if>
            <if test="identity != null "> and vu.identity = #{identity}</if>
            <if test="password != null  and password != ''"> and vu.password = #{password}</if>
            <if test="nickname != null  and nickname != ''"> and vu.nickname like concat('%', #{nickname}, '%')</if>
            <if test="username != null  and username != ''"> and vu.username like concat('%', #{username}, '%')</if>
            <if test="userimage != null  and userimage != ''"> and vu.userimage = #{userimage}</if>
            <if test="lastLoginTime != null "> and vu.last_login_time = #{lastLoginTime}</if>
            <if test="lastLoginIp != null  and lastLoginIp != ''"> and vu.last_login_ip = #{lastLoginIp}</if>
            <if test="coin != null "> and vu.coin = #{coin}</if>
            <if test="key != null "> and vu.`key` = #{key}</if>
            <if test="inviteCode != null  and inviteCode != ''"> and vu.invite_code = #{inviteCode}</if>
            <if test="steamId != null  and steamId != ''"> and vu.steam_id = #{steamId}</if>
            <if test="steamLink != null  and steamLink != ''"> and vu.steam_link = #{steamLink}</if>
            <if test="exp != null "> and vu.exp = #{exp}</if>
            <if test="level != null "> and vu.level = #{level}</if>
            <if test="seed != null  and seed != ''"> and vu.seed = #{seed}</if>
            <if test="isauth != null "> and vu.isauth = #{isauth}</if>
            <if test="state != null "> and vu.state = #{state}</if>
        </where>
        order by vu.create_time desc
    </select>
    
    <select id="selectVimUserById" parameterType="Long" resultMap="VimUserResult">
        <include refid="selectVimUserVo"/>
        where id = #{id}
    </select>

    <insert id="insertVimUser" parameterType="VimUser" useGeneratedKeys="true" keyProperty="id">
        insert into vim_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="phone != null and phone != ''">phone,</if>
            <if test="password != null and password != ''">password,</if>
            <if test="nickname != null and nickname != ''">nickname,</if>
            <if test="username != null and username != ''">username,</if>
            <if test="userimage != null">userimage,</if>
            <if test="lastLoginTime != null">last_login_time,</if>
            <if test="lastLoginIp != null">last_login_ip,</if>
            <if test="coin != null">coin,</if>
            <if test="key != null">`key`,</if>
            <if test="inviteCode != null">invite_code,</if>
            <if test="inviteUser != null">invite_user,</if>
            <if test="vimUsercreateTime != null">create_time,</if>
            <if test="steamId != null">steam_id,</if>
            <if test="steamLink != null">steam_link,</if>
            <if test="exp != null">exp,</if>
            <if test="level != null">level,</if>
            <if test="seed != null">seed,</if>
            <if test="identity != null">identity,</if>
            <if test="isauth != null">isauth,</if>
            <if test="state != null">state,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="password != null and password != ''">#{password},</if>
            <if test="nickname != null and nickname != ''">#{nickname},</if>
            <if test="username != null and username != ''">#{username},</if>
            <if test="userimage != null">#{userimage},</if>
            <if test="lastLoginTime != null">#{lastLoginTime},</if>
            <if test="lastLoginIp != null">#{lastLoginIp},</if>
            <if test="coin != null">#{coin},</if>
            <if test="key != null">#{key},</if>
            <if test="inviteCode != null">#{inviteCode},</if>
            <if test="inviteUser != null">#{inviteUser},</if>
            <if test="vimUsercreateTime != null">#{vimUsercreateTime},</if>
            <if test="steamId != null">#{steamId},</if>
            <if test="steamLink != null">#{steamLink},</if>
            <if test="exp != null">#{exp},</if>
            <if test="level != null">#{level},</if>
            <if test="seed != null">#{seed},</if>
            <if test="identity != null">#{identity},</if>
            <if test="isauth != null">#{isauth},</if>
            <if test="state != null">#{state},</if>
         </trim>
    </insert>

    <update id="updateVimUser" parameterType="VimUser">
        update vim_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="password != null and password != ''">password = #{password},</if>
            <if test="nickname != null and nickname != ''">nickname = #{nickname},</if>
            <if test="username != null and username != ''">username = #{username},</if>
            <if test="userimage != null">userimage = #{userimage},</if>
            <if test="lastLoginTime != null">last_login_time = #{lastLoginTime},</if>
            <if test="lastLoginIp != null">last_login_ip = #{lastLoginIp},</if>
            <if test="coin != null">coin = #{coin},</if>
            <if test="key != null">`key` = #{key},</if>
            <if test="inviteCode != null">invite_code = #{inviteCode},</if>
            <if test="inviteUser != null">invite_user = #{inviteUser},</if>
            <if test="vimUsercreateTime != null">create_time = #{vimUsercreateTime},</if>
            <if test="steamId != null">steam_id = #{steamId},</if>
            <if test="steamLink != null">steam_link = #{steamLink},</if>
            <if test="exp != null">exp = #{exp},</if>
            <if test="level != null">level = #{level},</if>
            <if test="seed != null">seed = #{seed},</if>
            <if test="identity != null">identity = #{identity},</if>
            <if test="isauth != null">isauth = #{isauth},</if>
            <if test="state != null">state = #{state},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVimUserById" parameterType="Long">
        delete from vim_user where id = #{id}
    </delete>

    <delete id="deleteVimUserByIds" parameterType="String">
        delete from vim_user where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 下级用户结果映射 -->
    <resultMap type="com.ruoyi.project.VimUserSys.domain.vo.SubordinateUserVO" id="SubordinateUserVOResult">
        <result property="id"    column="id"    />
        <result property="nickname"    column="nickname"    />
        <result property="username"    column="username"    />
        <result property="phone"    column="phone"    />
        <result property="userimage"    column="userimage"    />
        <result property="inviteCode"    column="invite_code"    />
        <result property="createTime"    column="create_time"    />
        <result property="lastLoginTime"    column="last_login_time"    />
        <result property="level"    column="level"    />
        <result property="exp"    column="exp"    />
        <result property="coin"    column="coin"    />
        <result property="key"    column="key"    />
        <result property="state"    column="state"    />
        <result property="identity"    column="identity"    />
        <result property="isauth"    column="isauth"    />
        <result property="totalRecharge"    column="total_recharge"    />
    </resultMap>

    <!-- 查询指定用户的下级用户列表 -->
    <select id="selectSubordinateUsers" parameterType="Long" resultMap="SubordinateUserVOResult">
        select
            u.id, u.nickname, u.username, u.phone, u.userimage, u.invite_code,
            u.create_time, u.last_login_time, u.level, u.exp, u.coin, u.`key`,
            u.state, u.identity, u.isauth,
            COALESCE(
                (SELECT SUM(r.amount) FROM vim_order_recharge r
                 WHERE r.uid = u.id AND r.state = 2), 0
            ) as total_recharge
        from vim_user u
        where u.invite_user = #{inviteUserId}
        order by u.create_time desc
    </select>

    <!-- 查询指定用户的下级用户列表（手动分页） -->
    <select id="selectSubordinateUsersWithPagination" resultMap="SubordinateUserVOResult">
        select
            u.id, u.nickname, u.username, u.phone, u.userimage, u.invite_code,
            u.create_time, u.last_login_time, u.level, u.exp, u.coin, u.`key`,
            u.state, u.identity, u.isauth,
            COALESCE(
                (SELECT SUM(r.amount) FROM vim_order_recharge r
                 WHERE r.uid = u.id AND r.state = 2), 0
            ) as total_recharge
        from vim_user u
        where u.invite_user = #{inviteUserId}
        order by u.create_time desc
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <!-- 统计指定用户的下级用户数量 -->
    <select id="countSubordinateUsers" parameterType="Long" resultType="int">
        select count(*) from vim_user where invite_user = #{inviteUserId}
    </select>

    <!-- 检查用户是否已进行实名认证 -->
    <select id="checkUserAuthStatus" parameterType="Long" resultType="int">
        SELECT COUNT(1)
        FROM vim_user_auth
        WHERE uid = #{userId}
    </select>

    <!-- 查询最大用户ID -->
    <select id="selectMaxUserId" resultType="Long">
        SELECT MAX(id) FROM vim_user
    </select>
</mapper>